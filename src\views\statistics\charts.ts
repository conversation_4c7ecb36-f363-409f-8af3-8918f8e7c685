import numberPrecision from "number-precision";
import * as echarts from "echarts/core";

import {
  DatasetComponent,
  DatasetComponentOption,
  TitleComponent,
  TitleComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  GridComponent,
  GridComponentOption,
  TransformComponent,
  MarkLineComponent,
  MarkLineComponentOption,
  DataZoomComponent,
  LegendComponent,
  ToolboxComponent,
  GraphicComponent,
  GraphicComponentOption
} from "echarts/components";
import { Boxplot<PERSON><PERSON>, BoxplotSeriesOption, Scatter<PERSON><PERSON>, ScatterSeriesOption, LineChart, BarChart } from "echarts/charts";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";

echarts.use([
  DatasetComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  TransformComponent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>vas<PERSON><PERSON><PERSON>,
  <PERSON>Transition,
  <PERSON><PERSON><PERSON><PERSON>omponent,
  DataZoomComponent,
  <PERSON><PERSON><PERSON>ponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ToolboxComponent,
  GraphicComponent
]);

type EChartsOption = echarts.ComposeOption<
  | DatasetComponentOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | BoxplotSeriesOption
  | ScatterSeriesOption
  | MarkLineComponentOption
  | GraphicComponentOption
>;

let chartObj: any = {};
export function boxplot({ ref, list, title, Mean, histogramCalculate, USL, LSL, Target }: any) {
  if (chartObj[ref.id]) {
    chartObj[ref.id].dispose();
  }

  if (list.length < 1) {
    return;
  }
  // 初始化图表
  chartObj[ref.id] = echarts.init(ref);
  let option: EChartsOption;

  // console.log(mean);
  const _list = list.map((item: any) => item.list);
  const flatArray = _list.flat();
  const max = Math.max(...flatArray);
  const min = Math.min(...flatArray);
  const scale = 0.1;
  const maxValue = (max * scale + max).toFixed(2);
  const minValue = (min - min * scale).toFixed(2);
  const names = list.map((item: any) => item.date);

  // list: candlestickDataList,
  //   // mean: histogramCalculate.Mean,
  //   lsl: true,
  //   usl: true,
  //   nominal: true,
  //   mean: true,
  //   histogramCalculate

  const markLine: any = {
    z: 999999,
    data: [],
    symbol: "none" // 关键：去除尾端箭头
  };

  if (Mean) {
    markLine.data.push({
      name: `Theoretical Mean`,
      symbol: "none", // 无端点箭头
      silent: false,
      z: 999999,
      yAxis: histogramCalculate.Mean,
      lineStyle: {
        color: "#FF6B6B",
        type: "solid",
        width: 2
      },
      label: {
        formatter: `Theoretical Mean: ${histogramCalculate.Mean}`,
        position: "middle",
        color: "#666"
      }
    });
  }

  if (USL) {
    markLine.data.push({
      name: "USL",
      symbol: "none", // 无端点箭头
      silent: false,
      yAxis: histogramCalculate.USL,
      lineStyle: {
        color: "#FF6B6B",
        type: "dashed",
        width: 2,
        cap: "none"
      },
      label: {
        formatter: `USL`,
        position: "end",
        color: "#FF6B6B"
      }
    });
  }

  if (LSL) {
    markLine.data.push({
      name: "LSL",
      symbol: "none", // 无端点箭头
      silent: false,
      yAxis: histogramCalculate.LSL,
      lineStyle: {
        color: "#FF6B6B",
        type: "solid",
        width: 2,
        cap: "none"
      },
      label: {
        formatter: `LSL`,
        position: "end",
        color: "#FF6B6B"
      }
    });
  }
  if (Target) {
    markLine.data.push({
      name: "LSL",
      symbol: "none", // 无端点箭头
      silent: false,
      yAxis: histogramCalculate.Target,
      lineStyle: {
        color: "green",
        type: "solid",
        width: 2
      },
      label: {
        formatter: `Nominal`,
        position: "end",
        color: "green"
      }
    });
  }

  option = {
    title: [
      {
        text: title,
        left: "center"
      }
      // {
      //   text: "upper: Q3 + 1.5 * IQR \nlower: Q1 - 1.5 * IQR",
      //   borderColor: "#999",
      //   borderWidth: 1,
      //   textStyle: {
      //     fontWeight: "normal",
      //     fontSize: 14,
      //     lineHeight: 20
      //   },
      //   left: "10%",
      //   top: "90%"
      // }
    ],
    dataset: [
      {
        source: _list
      },
      {
        transform: {
          type: "boxplot",
          config: { itemNameFormatter: ({ value }: any) => names[value] }
        }
      },
      {
        fromDatasetIndex: 1,
        fromTransformResult: 1
      }
    ],
    tooltip: {
      trigger: "item",
      axisPointer: {
        type: "shadow"
      }
    },
    grid: {
      left: "15%",
      right: "10%",
      bottom: "15%",
      top: "20%"
    },
    xAxis: {
      type: "category",
      boundaryGap: true,
      nameGap: 30,
      splitArea: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: "value",
      name: "Observations",
      splitArea: {
        show: false
      },
      min: minValue, // 扩展20%范围
      max: maxValue,
      // interval: range
      scale: true // 自动扩展Y轴范围以包含所有数据点
    },
    series: [
      {
        name: "",
        type: "boxplot",
        datasetIndex: 1,
        // 添加红色参考线配置
        markLine: markLine
      },
      {
        name: "outlier",
        type: "scatter",
        datasetIndex: 2
      }
    ]
  };

  chartObj[ref.id].setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", function () {
    chartObj[ref.id].resize();
  });
}

export const zftChart = ({ ref, data, callback }: any) => {
  // 模拟数据
  if (chartObj[ref.id]) {
    chartObj[ref.id].dispose();
  }

  if (data.histogramDataList.length < 1) {
    return;
  }
  // 初始化图表
  chartObj[ref.id] = echarts.init(ref);

  let option: EChartsOption;
  // 提取数据
  const calc = data.histogramCalculate;
  const rawData = data.histogramDataList;
  const binNumber = 10; // 柱子数量
  const min = Math.min(...rawData);
  const max = Math.max(...rawData);
  const binWidth = (max - min) / binNumber; // 柱子宽度

  // 计算频数分布
  const bins = Array(binNumber).fill(0);
  rawData.forEach(value => {
    const index = Math.min(Math.floor((value - min) / binWidth), binNumber - 1);
    bins[index]++;
  });

  // 计算直方图中心点（X轴位置）
  const binCenters = [];
  for (let i = 0; i < binNumber; i++) {
    binCenters.push(min + (i + 0.5) * binWidth);
  }

  // 正态分布密度函数
  function normalPDF(x, mean, stdDev) {
    const exponent = -Math.pow(x - mean, 2) / (2 * Math.pow(stdDev, 2));
    return (1 / (stdDev * Math.sqrt(2 * Math.PI))) * Math.exp(exponent);
  }

  // 生成正态曲线点
  function generateNormalCurve(mean, stdDev) {
    const points = [];
    // 曲线范围（均值±3标准差）
    const start = mean - 3 * stdDev;
    const end = mean + 3 * stdDev;
    const step = (end - start) / 200;

    for (let x = start; x <= end; x += step) {
      // 将概率密度转换为频数（乘以总样本数和柱子宽度）
      const y = normalPDF(x, mean, stdDev) * calc.n * binWidth;
      points.push([x, y]);
    }
    return points;
  }

  // 生成两条正态曲线
  const curveData1 = generateNormalCurve(calc.Mean, calc.SigmaWithin);
  const curveData2 = generateNormalCurve(calc.Mean, calc.SigmaTotal);

  const inGroupName = "Potential (Within-Group) Capability";
  const allGroupName = "Overall Capability";
  // 初始化ECharts

  option = {
    // title: {
    //   text: "数据分布与正态曲线分析",
    //   subtext: "组内与整体正态分布对比",
    //   left: "center",
    //   textStyle: {
    //     fontSize: 20,
    //     fontWeight: "bold"
    //     // color: "#ecf0f1"
    //   },
    //   subtextStyle: {
    //     // color: "#bdc3c7"
    //   }
    // },
    // graphic: {
    //   type: "text",
    //   left: "left",
    //   top: "top",
    //   //          <!--            <p>规格下限:2</p>-->
    //   //           <!--            <p>规格目标:12</p>-->
    //   //           <!--            <p>规格上限:22</p>-->
    //   //           <!--            <p>样本均值:12.6135</p>-->
    //   //           <!--            <p>样本数量:32</p>-->
    //   //           <!--            <p>标准差(总体):3.9274</p>-->
    //   //           <!--            <p>标准差(组内):4.0038AD.0.5728</p>-->
    //   //           <!--            <p>p值:0.1301</p>-->
    //   //           <!--            <p>变异系数:0.3114</p>-->
    //   style: {
    //     text:
    //       `规格下限：2\n` +
    //       `规格目标：2\n` +
    //       `规格上限：2\n` +
    //       `样本均值：2\n` +
    //       `样本数量：2\n` +
    //       `标准差(总体)：2\n` +
    //       `标准差(组内)：2\n` +
    //       `p值：2\n` +
    //       `变异系数：2\n`,
    //     fontSize: 12,
    //     fill: "#666",
    //     lineHeight: 16,
    //     backgroundColor: "white"
    //   }
    // },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985"
        }
      },
      formatter: function (params) {
        let result = "";
        params.forEach(param => {
          if (param.componentSubType === "bar") {
            // const lowerBound = (param.value[0] - binWidth / 2).toFixed(5);
            // const upperBound = (param.value[0] + binWidth / 2).toFixed(5);
            // result += `区间: ${lowerBound} - ${upperBound}<br/>`;
            // result += `频数: ${param.value[1]}<br/>`;
          } else if (param.seriesName === inGroupName || param.seriesName === allGroupName) {
            result += `${param.seriesName}<br/>`;
            result += `Value: ${param.value[0].toFixed(5)}<br/>`;
            result += `频数: ${param.value[1].toFixed(1)}<br/>`;
          } else if (param.componentSubType === "scatter") {
            result += `${param.seriesName}: ${param.value[0].toFixed(5)}<br/>`;
          }
        });
        return result;
      }
    },
    // legend: {
    //   data: [inGroupName, allGroupName],
    //   top: 0,
    //   right: 0,
    //   show: true,
    //   formatter: function (name) {
    //     return (
    //       `{title|${name}}\n` +
    //       `{value|Pp:0.8487}\n` +
    //       `{value|PPL:0.9008}\n` +
    //       `{value|PPU:0.7967}\n` +
    //       `{value|Ppk:0.7967}\n` +
    //       `{value|Cpm:0.8517}\n` +
    //       `{value|Ca:0.0614}`
    //     );
    //   },
    //   align: "left",
    //   orient: "vertical",
    //   // width: 5,
    //   // height: 5,
    //   textStyle: {
    //     fontSize: 12,
    //     rich: {
    //       title: { fontSize: 14, fontWeight: "bold", color: "#333" },
    //       value: { fontSize: 12, color: "#666", lineHeight: 14 }
    //     }
    //   }
    // },
    grid: {
      top: 100,
      bottom: "15%",
      left: "20%",
      right: 50
    },
    xAxis: {
      type: "value",
      name: "",
      // nameLocation: "middle",
      // nameGap: 30,
      // min: calc.LSL - 0.001,
      // max: calc.USL + 0.001,
      axisLine: {
        lineStyle: {
          // color: "#95a5a6"
        }
      },
      axisLabel: {
        // color: "#ecf0f1",
        formatter: function (value) {
          return value.toFixed(4);
        }
      },
      splitLine: {
        lineStyle: {
          color: "rgba(127, 127, 127, 0.3)"
        }
      },
      max: "dataMax",
      min: "dataMin"
    },
    yAxis: {
      type: "value",
      // name: "频数",
      // nameRotate: 0,
      position: "left",
      // nameGap: 45,
      axisLine: {
        lineStyle: {
          // color: "#95a5a6"
        }
      },
      axisLabel: {
        // color: "#ecf0f1"
        formatter: value => {
          return value.toFixed(2);
        }
      },
      splitLine: {
        lineStyle: {
          // color: "rgba(127, 127, 127, 0.3)"
        }
      },
      max: "dataMax",
      min: "dataMin",
      // interval: 1,
      scale: true // 自动扩展Y轴范围以包含所有数据点
    },
    series: [
      // 直方图（柱状图）
      {
        name: "",
        type: "bar",
        data: binCenters.map((center, idx) => [center, bins[idx]]),
        // barWidth: binWidth * 0.9, // 设置柱宽接近bin宽度（小间隙）
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#5470c6" },
            { offset: 1, color: "#3ba1ff" }
          ]),
          borderRadius: [0, 0, 0, 0] // 直角边
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#3ba1ff" },
              { offset: 1, color: "#5470c6" }
            ])
          }
        }
      },
      // 第一条正态曲线（组内）
      {
        name: inGroupName,
        type: "line",
        symbol: "none",
        smooth: true,
        lineStyle: {
          width: 2,
          color: "green",
          type: "dashed"
        },
        data: curveData1
      },
      // 第二条正态曲线（整体）
      {
        name: allGroupName,
        type: "line",
        symbol: "none",
        smooth: true,
        lineStyle: {
          width: 2,
          color: "red",
          type: "dashed"
        },
        data: curveData2
      }
      // 规格线标记
      // {
      //   name: "USL",
      //   type: "scatter",
      //   symbol: "diamond",
      //   symbolSize: 16,
      //   data: [[calc.USL, 0]],
      //   itemStyle: {
      //     color: "#e74c3c"
      //   },
      //   label: {
      //     show: true,
      //     position: "top",
      //     formatter: "USL: {c}",
      //     color: "#e74c3c",
      //     fontWeight: "bold"
      //   }
      // },
      // {
      //   name: "LSL",
      //   type: "scatter",
      //   symbol: "diamond",
      //   symbolSize: 16,
      //   data: [[calc.LSL, 0]],
      //   itemStyle: {
      //     color: "#2ecc71"
      //   },
      //   label: {
      //     show: true,
      //     position: "top",
      //     formatter: "LSL: {c}",
      //     color: "#2ecc71",
      //     fontWeight: "bold"
      //   }
      // },
      // {
      //   name: "Target",
      //   type: "scatter",
      //   symbol: "diamond",
      //   symbolSize: 16,
      //   data: [[calc.Target, 0]],
      //   itemStyle: {
      //     color: "#f39c12"
      //   },
      //   label: {
      //     show: true,
      //     position: "top",
      //     formatter: "Target: {c}",
      //     color: "#f39c12",
      //     fontWeight: "bold"
      //   }
      // },
      // {
      //   name: "均值",
      //   type: "scatter",
      //   symbol: "circle",
      //   symbolSize: 14,
      //   data: [[calc.Mean, 0]],
      //   itemStyle: {
      //     color: "#9b59b6"
      //   },
      //   label: {
      //     show: true,
      //     position: "top",
      //     formatter: "Mean: {c}",
      //     color: "#9b59b6",
      //     fontWeight: "bold"
      //   }
      // }
    ]
    // dataZoom: [
    //   {
    //     type: "inside",
    //     xAxisIndex: 0,
    //     start: 0,
    //     end: 100
    //   },
    //   {
    //     type: "slider",
    //     xAxisIndex: 0,
    //     bottom: 20,
    //     start: 0,
    //     end: 100
    //   }
    // ]
  };

  chartObj[ref.id].setOption(option);

  if (typeof callback === "function") {
    callback();
  }

  // 响应窗口大小变化
  window.addEventListener("resize", function () {
    chartObj[ref.id].resize();
  });
};

export function ipqaYield({ ref, list }: any) {
  if (chartObj[ref.id]) {
    chartObj[ref.id].dispose();
  }

  if (list.length < 1) {
    return;
  }

  let names = [];
  let lineData = [];
  let barData = [];
  const maxBarWidth = 50;
  const minBarWidth = 10;
  const barWidth = Math.min(Math.max(maxBarWidth / list.length, minBarWidth), maxBarWidth);

  for (let j = 0; j < list.length; j++) {
    names.push(list[j].date);
    lineData.push(list[j].yieldRate);
    barData.push(list[j].outputQty);
  }

  // 初始化图表
  chartObj[ref.id] = echarts.init(ref);
  let option: EChartsOption;
  option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999"
        }
      }
    },
    legend: {
      data: ["Output", "Yield"],
      show: false
    },
    xAxis: [
      {
        type: "category",
        data: names,
        axisPointer: {
          type: "shadow"
        }
      }
    ],
    yAxis: [
      {
        type: "value",
        // name: "Output",
        axisLabel: {
          formatter: "{value}"
        }
      },
      {
        type: "value",
        // name: "Yield",
        axisLabel: {
          formatter: "{value}"
        }
      }
    ],
    series: [
      {
        name: "Output",
        type: "bar",
        tooltip: {
          valueFormatter: function (value) {
            return (value as number) + "";
          }
        },
        data: barData,
        barWidth: barWidth
      },
      {
        name: "Yield",
        type: "line",
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value as number;
          }
        },
        data: lineData
      }
    ]
  };
  chartObj[ref.id].setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", function () {
    chartObj[ref.id].resize();
  });
}

export function defectParetoChart({ ref, list }: any) {
  if (!ref) return;
  if (chartObj[ref?.id]) {
    chartObj[ref.id].dispose();
  }

  if (list.length < 1) {
    return;
  }

  const maxBarWidth = 50;
  const minBarWidth = 10;

  const _list = [...list].sort((a, b) => b.value - a.value);

  const barWidth = Math.min(Math.max(maxBarWidth / _list.length, minBarWidth), maxBarWidth);

  const total = _list.reduce((sum, item) => sum + item.percentage, 0);

  // let cumulativePercent = 0;
  // _list.forEach(item => {
  //   // cumulativePercent += (item.percentage / total) * 100;
  //   cumulativePercent = numberPrecision.plus(
  //     numberPrecision.times(numberPrecision.divide(item.percentage, total), 100),
  //     cumulativePercent
  //   );
  //   item.cumulativePercent = cumulativePercent.toFixed(2);
  // });
  let lineData = _list.map(item => item.cumulativePercentage);
  let barData = _list.map(item => item.defectQty);
  let names = _list.map(item => item.defectCategory);
  // 初始化图表
  chartObj[ref.id] = echarts.init(ref);
  let option: EChartsOption;
  option = {
    title: { text: "Defect Category", left: "center" },
    tooltip: { trigger: "axis" },
    legend: { data: ["Pareto", "Defect Qty"], bottom: 0 },
    xAxis: {
      type: "category",
      data: names
    },
    yAxis: [
      { type: "value", name: "Defect Qty" },
      {
        type: "value",
        name: "Pareto",
        // min: 0,
        // max: 100,
        axisLabel: { formatter: "{value}%" },
        max: "dataMax"
      }
    ],
    series: [
      {
        name: "Defect Qty",
        type: "bar",
        data: barData,
        barWidth
      },
      {
        name: "Pareto",
        type: "line",
        yAxisIndex: 1,
        data: lineData,
        symbol: "circle",
        label: { show: true, formatter: "{c}%" }
        // markLine: {
        //   data: [{ yAxis: 80 }],
        //   lineStyle: { type: "dashed", color: "red" },
        //   label: { formatter: "80% 关键阈值" }
        // }
      }
    ]
  };
  chartObj[ref.id].setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", function () {
    chartObj[ref.id].resize();
  });
}

export const larChart = ({ ref, list }: any) => {
  if (chartObj[ref.id]) {
    chartObj[ref.id].dispose();
  }

  if (list.length < 1) {
    return;
  }

  const maxBarWidth = 50;
  const minBarWidth = 10;

  // const _list = [...list].sort((a, b) => b.value - a.value);

  const barWidth = Math.min(Math.max(maxBarWidth / list.length, minBarWidth), maxBarWidth);

  const barData = list.map(item => item.lar);
  const names = list.map(item => item.date);
  chartObj[ref.id] = echarts.init(ref);
  let option: EChartsOption;
  option = {
    // title: { text: "Lar", left: "center" },
    tooltip: { trigger: "axis" },
    legend: { data: ["Lar"], bottom: 0 },
    xAxis: {
      type: "category",
      data: names
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        name: "Lar",
        type: "bar",
        data: barData,
        barWidth
      }
    ]
  };
  chartObj[ref.id].setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", function () {
    chartObj[ref.id].resize();
  });
};
