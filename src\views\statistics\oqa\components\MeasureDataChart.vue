<template>
  <div class="card" style="margin-bottom: 10px; margin-top: 10px">
    <el-row>
      <el-col :span="12">
        <div class="chart-item" id="oqaBoxPlotRef" ref="oqaBoxPlotRef"></div>
      </el-col>
      <el-col :span="12">
        <div style="position: relative">
          <paramsPopper v-if="showParams" :histogram-calculate="reportData?.histogramCalculate" />
          <div class="chart-item" id="oqaZftRef" ref="oqaZftRef"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script setup lang="ts" name="ipqaMeasureData">
import { onMounted, ref } from "vue";
import { ProductionDataAnalysis } from "@/typings/productionDataAnalysis.d";
import { boxplot, zftChart } from "@/views/statistics/charts";
import { oqaMeasureData } from "@/api/modules/productionDataAnalysis";
import ParamsPopper from "@/views/statistics/paramsPopper.vue";
const reportData = ref<ProductionDataAnalysis>();
const oqaBoxPlotRef = ref();
const oqaZftRef = ref();
const props = withDefaults(defineProps<{ queryParams: any }>(), {});
const showParams = ref(false);
const getData = async (query: any) => {
  showParams.value = false;
  const res = await oqaMeasureData({
    startDate: "2024-01-17",
    endDate: "2026-02-10",
    supplier: "",
    product: "PLATE,BOTTOM,VCM,4D V11",
    model: "726235100-P",
    parameter: "7-2",
    doNo: "",
    weekCode: ""
  });
  reportData.value = res.data;

  if (!res.data) {
    reportData.value = {
      candlestickTitle: "",
      histogramDataList: [],
      candlestickDataList: [],
      histogramCalculate: {
        USL: 0,
        LSL: 0,
        Target: 0,
        Mean: 0,
        n: 0,
        SigmaWithin: 0,
        SigmaTotal: 0,
        CV: 0,
        AD: 0,
        p: 0,
        normalDistribution: true,
        Ca: 0,
        Cp: 0,
        Cpk: 0,
        Pp: 0,
        Ppk: 0,
        Cpm: 0,
        PPL: 0,
        PPU: 0,
        CPL: 0,
        CPU: 0
      }
    };
  }

  if (!Array.isArray(res.data?.histogramDataList)) {
    reportData.value!.histogramDataList = [];
  }
  if (!Array.isArray(res.data?.candlestickDataList)) {
    reportData.value!.candlestickDataList = [];
  }

  setChart();

  // window.addEventListener("resize", () => {
  //   setChart();
  // });
};

const setChart = () => {
  const { candlestickTitle, candlestickDataList, histogramCalculate } = reportData.value!;
  /**
   * boxNames 箱图 X轴
   * boxList 数据集
   */
  let boxNames: string[] = [];
  let boxList: any[] = [];
  if (Array.isArray(candlestickDataList)) {
    for (let j = 0; j < candlestickDataList.length; j++) {
      boxNames.push(candlestickDataList[j].date);
      boxList.push(candlestickDataList[j].list);
    }
  }

  boxplot({
    ref: oqaBoxPlotRef.value,
    title: candlestickTitle,
    // names: boxNames,
    list: candlestickDataList,
    // mean: histogramCalculate.Mean,
    Mean: true,
    histogramCalculate
  });
  zftChart({
    ref: oqaZftRef.value,
    data: reportData.value,
    callback: () => {
      showParams.value = true;
    }
  });
};

defineExpose({ getData });
</script>
<style scoped lang="scss">
.chart-item {
  height: 400px;
  width: 100%;
  margin-top: 30px;
}

.left-params {
  position: absolute;
  top: -30px;
  left: 0;
  line-height: 16px;
  font-size: 12px;
  z-index: 111;
  ::v-deep(.el-card__body) {
    padding: 10px;
  }
}
.right-params {
  position: absolute;
  top: -30px;
  right: 0;
  line-height: 16px;
  font-size: 12px;
  z-index: 111;
  .title {
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 15px;
  }
  ::v-deep(.el-card__body) {
    padding: 10px;
  }
}

.lengend {
  margin-top: 5px;
  .item {
    display: inline-block;
    //font-weight: bold;
    font-size: 14px;
    position: relative;
    margin-right: 5px;
    &:before {
      content: "";
      display: inline-block;
      width: 16px;
      height: 0;
      border-top: 3px dashed red;
      vertical-align: middle;
      margin-right: 4px;
    }
    &:nth-child(2) {
      &:before {
        border-color: green;
      }
    }
  }
}
</style>
