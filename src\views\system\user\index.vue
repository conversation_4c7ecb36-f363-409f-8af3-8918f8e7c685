<template>
  <div class="main-box">
    <TreeFilter label="label" value="id" :request-api="getDeptAll" :default-value="initParam.deptId" @change="changeTreeFilter" />

    <div class="table-box">
      <ProTable
        ref="proTable"
        :columns="columns"
        :request-api="getTableList"
        :init-param="initParam"
        :data-callback="dataCallback"
        @reset="reset"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader>
          <el-button v-auth="'system:user:add'" type="primary" @click="openDictModal('新增')">{{ $t("新增") }}</el-button>
          <el-button v-auth="'system:user:edit'" type="primary" @click="openDictModal('编辑')">{{ $t("编辑") }}</el-button>
          <el-button v-auth="'system:user:edit'" type="primary" @click="handleResetPassword">{{ $t("重置密码") }}</el-button>
        </template>
        <template #footerBtn="{ selectedListIds, isSelected }">
          <el-button
            v-auth="'system:user:delete'"
            :disabled="!isSelected"
            type="danger"
            plain
            @click="batchDelete(selectedListIds as number[])"
          >
            {{ $t("批量删除") }}
          </el-button>
        </template>
      </ProTable>
      <DictModal ref="EditModalRef" />
    </div>
  </div>
</template>

<script setup lang="tsx" name="DictTable">
import TreeFilter from "@/components/TreeFilter/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { ReqPage, ResPage } from "@/api/interface";
import DictModal from "./components/EditModal.vue";
import { useDateFormat } from "@vueuse/core";
import { useDict } from "@/hooks/useDict";
// import { Dict } from "@/typings/dict";
import { isArray, isEmpty } from "@/utils/is";
import { ref, reactive, toRefs, onMounted, nextTick } from "vue";
// import { useAuthStore } from "@/stores/modules/auth";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useRouter } from "vue-router";
import { Staff } from "@/typings/staff";
import {
  importStaffTemp,
  getStaffList,
  createStaff,
  deleteStaff,
  exportStaff,
  importStaff,
  auditStaff,
  editStaff,
  setStaffRole,
  userResetPwd
} from "@/api/modules/staff";

import { getDeptAll, getDeptList } from "@/api/modules/dept";
import { getOperationColWidth, handleTree, visibleOperationCol } from "@/utils";
import EditModal from "@/views/system/template/components/EditModal.vue";
import { Supplier } from "@/typings/supplier";
import { getSupplierAll } from "@/api/modules/supplier";
import { formatParams } from "@/utils/util";
import { useI18n } from "vue-i18n";
const router = useRouter();
// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const EditModalRef = ref<InstanceType<typeof EditModal> | null>(null);

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<Partial<Staff.Item & ReqPage>>({ pageNum: 1, pageSize: 10 });

const { common_status } = useDict("common_status");
const supplierList = ref<Supplier.Item[]>([]);

// const pageButtons = ["dict:edit", "dict:delete"];
//
// const auth = useAuthStore();
const treeData = ref<any[]>([]);
const { t } = useI18n();
// const cascaderProp = { label: "label", value: "id", children: "children", checkStrictly: true };
// 表格配置项

const columns = reactive<ColumnProps<Staff.Item>[]>([
  { type: "selection", fixed: "left", width: 60 },
  { type: "index", label: t("序号"), width: 80 },
  {
    prop: "supplier",
    label: "供应商",
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.supplier} placeholder={t("请选择")} clearable>
            {supplierList.value.map(item => (
              <el-option key={item.id} label={item.supplier} value={item.supplier} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "dept.deptName",
    label: "部门",
    width: 150,
    search: {
      render: () => {
        return (
          <el-tree-select
            v-model={initParam.deptId}
            data={treeData.value}
            props={{ value: "id", label: "label", children: "children", checkStrictly: true }}
            value-key="id"
            placeholder={t("请选择")}
            check-strictly
          />
        );
      }
    }
    // enum: treeData,
    // fieldNames: { label: "label", value: "id" }
  },
  { prop: "nickName", label: "姓名" },
  { prop: "userName", label: "账号" },
  {
    prop: "status",
    label: "状态",
    tag: false,
    enum: common_status,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.status} placeholder={t("请选择")} clearable>
            {common_status.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    },
    fieldNames: { label: "label", value: "value" }
  },
  {
    prop: "createdTime",
    label: "创建时间",
    render: ({ row }) => <span>{useDateFormat(row.createdTime, "YYYY-MM-DD HH:mm:ss").value}</span>
  }
  // ...(visibleOperationCol(auth.authButtonList, pageButtons)
  //   ? [
  //       {
  //         prop: "operation",
  //         label: "操作",
  //         width: getOperationColWidth(auth.authButtonList, pageButtons),
  //         fixed: "right"
  //       }
  //     ]
  //   : [])
]);
const selectable = (row: any) => !row.admin;
const dataCallback = (data: ResPage<Staff.Item>) => {
  return {
    list: data.list?.map(item => {
      item.id = item.userId;
      return item;
    }),
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: Staff.Item & ReqPage) => {
  initParam.pageNum = params.pageNum;
  initParam.pageSize = params.pageSize;
  return getStaffList(initParam);
};

const batchDelete = async (id: number | number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(deleteStaff, ids.join(","), t("删除所选信息"));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const openDictModal = (title: string, row: Partial<Staff.Item> = {}) => {
  if (title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    form,
    common_status: common_status.value,
    api: title === "新增" ? createStaff : title === "编辑" ? editStaff : void 0,
    getTableList: proTable.value?.getTableList
  };
  if (isEmpty(form.userId)) Object.assign(params.form, { status: "0" });
  EditModalRef.value?.acceptParams(params);
};

const handleResetPassword = async (id: number | number[]) => {
  check();
  const params = {
    userId: currentRow.value.userId,
    password: "123456"
  };
  await useHandleData(userResetPwd, params, t("重置密码 初始密码：") + "123456");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const getDept = async () => {
  let { data } = await getDeptAll();
  treeData.value = data;
};

const changeTreeFilter = (val: number) => {
  initParam.deptId = val;
  nextTick(proTable.value?.getTableList);
};

const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};

const reset = async () => {
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  await nextTick(proTable.value?.getTableList);
};
onMounted(async () => {
  await getSupplierData();
  await getDept();
});
</script>
