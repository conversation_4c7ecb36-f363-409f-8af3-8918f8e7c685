<template>
  <div class="card" style="margin-bottom: 10px; margin-top: 10px">
    <el-row>
      <el-col :span="12">
        <div class="chart-item" id="oqaBoxPlotRef" ref="oqaBoxPlotRef"></div>
      </el-col>
      <el-col :span="12">
        <div style="position: relative">
          <paramsPopper v-if="showParams" :histogram-calculate="reportData?.histogramCalculate" />
          <div class="chart-item" id="ipqaZftRef" ref="ipqaZftRef"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script setup lang="ts" name="ipqaMeasureData">
import { onMounted, ref } from "vue";
import { ProductionDataAnalysis } from "@/typings/productionDataAnalysis.d";
import { boxplot, zftChart } from "@/views/statistics/charts";
import { ipqaMeasureData } from "@/api/modules/productionDataAnalysis";
import paramsPopper from "@/views/statistics/paramsPopper.vue";
const reportData = ref<ProductionDataAnalysis>();
const oqaBoxPlotRef = ref();
const ipqaZftRef = ref();
const props = withDefaults(defineProps<{ queryParams: any }>(), {});
const showParams = ref(false);
const getData = async (query: any) => {
  showParams.value = false;
  // const res = await ipqaMeasureData(query);
  const res = {
    msg: "操作成功",
    code: 200,
    data: {
      candlestickTitle: null,
      candlestickDataList: [
        {
          date: "2025-02-04",
          list: [
            0.13068, 0.13065, 0.13085, 0.13085, 0.13046, 0.13076, 0.13073, 0.13065, 0.13076, 0.13052, 0.13058, 0.13072, 0.13075,
            0.13068, 0.13078, 0.13066, 0.13073, 0.13061, 0.13057, 0.13071, 0.13064, 0.13057, 0.13066, 0.1307, 0.13068, 0.13077,
            0.13079, 0.13074, 0.13068, 0.13069, 0.13061, 0.13079
          ]
        }
      ],
      histogramCalculate: {
        USL: 0.132,
        LSL: 0.128,
        Target: 0.13,
        Mean: 0.1307,
        n: 32,
        SigmaWithin: 0.0001,
        SigmaTotal: 0.0001,
        CV: 0.0676,
        AD: 0.2253,
        p: 0.7632,
        normalDistribution: true,
        Ca: 0.3441,
        Cp: 7.4313,
        Cpk: 4.8745,
        Pp: 7.5502,
        Ppk: 4.9525,
        Cpm: 0.9609,
        PPL: 10.148,
        PPU: 4.9525,
        CPL: 9.9881,
        CPU: 4.8745
      },
      histogramDataList: [
        0.13068, 0.13065, 0.13085, 0.13085, 0.13046, 0.13076, 0.13073, 0.13065, 0.13076, 0.13052, 0.13058, 0.13072, 0.13075,
        0.13068, 0.13078, 0.13066, 0.13073, 0.13061, 0.13057, 0.13071, 0.13064, 0.13057, 0.13066, 0.1307, 0.13068, 0.13077,
        0.13079, 0.13074, 0.13068, 0.13069, 0.13061, 0.13079
      ]
    }
  };
  reportData.value = res.data;

  if (!res.data) {
    reportData.value = {
      candlestickTitle: "",
      histogramDataList: [],
      candlestickDataList: [],
      histogramCalculate: {
        USL: 0,
        LSL: 0,
        Target: 0,
        Mean: 0,
        n: 0,
        SigmaWithin: 0,
        SigmaTotal: 0,
        CV: 0,
        AD: 0,
        p: 0,
        normalDistribution: true,
        Ca: 0,
        Cp: 0,
        Cpk: 0,
        Pp: 0,
        Ppk: 0,
        Cpm: 0,
        PPL: 0,
        PPU: 0,
        CPL: 0,
        CPU: 0
      }
    };
  }

  if (!Array.isArray(res.data?.histogramDataList)) {
    reportData.value!.histogramDataList = [];
  }
  if (!Array.isArray(res.data?.candlestickDataList)) {
    reportData.value!.candlestickDataList = [];
  }

  setChart();

  // window.addEventListener("resize", () => {
  //   setChart();
  // });
};

const setChart = () => {
  const { candlestickTitle, candlestickDataList, histogramCalculate } = reportData.value;
  /**
   * boxNames 箱图 X轴
   * boxList 数据集
   */
  // let boxNames: string[] = [];
  // let boxList: any[] = [];
  // if (Array.isArray(candlestickDataList)) {
  //   for (let j = 0; j < candlestickDataList.length; j++) {
  //     boxNames.push(candlestickDataList[j].date);
  //     boxList.push(candlestickDataList[j].list);
  //   }
  // }

  boxplot({
    ref: oqaBoxPlotRef.value,
    title: candlestickTitle,
    // names: boxNames,
    list: candlestickDataList,
    // mean: histogramCalculate.Mean,
    LSL: true,
    USL: true,
    nominal: true,
    Target: true,
    histogramCalculate
  });
  zftChart({
    ref: ipqaZftRef.value,
    data: reportData.value,
    callback: () => {
      showParams.value = true;
    }
  });
};

defineExpose({ getData });
</script>
<style scoped lang="scss">
.chart-item {
  height: 400px;
  width: 100%;
  margin-top: 30px;
}

.left-params {
  position: absolute;
  top: -30px;
  left: 0;
  line-height: 16px;
  font-size: 12px;
  z-index: 111;
  ::v-deep(.el-card__body) {
    padding: 10px;
  }
}
.right-params {
  position: absolute;
  top: -30px;
  right: 0;
  line-height: 16px;
  font-size: 12px;
  z-index: 111;
  .title {
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 15px;
  }
  ::v-deep(.el-card__body) {
    padding: 10px;
  }
}

.lengend {
  margin-top: 5px;
  .item {
    display: inline-block;
    //font-weight: bold;
    font-size: 14px;
    position: relative;
    margin-right: 5px;
    &:before {
      content: "";
      display: inline-block;
      width: 16px;
      height: 0;
      border-top: 3px dashed red;
      vertical-align: middle;
      margin-right: 4px;
    }
    &:nth-child(2) {
      &:before {
        border-color: green;
      }
    }
  }
}
</style>
