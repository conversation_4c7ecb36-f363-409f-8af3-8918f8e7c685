import axios, { AxiosInstance, AxiosError, AxiosRequestConfig, InternalAxiosRequestConfig, AxiosResponse } from "axios";
import { showFullScreenLoading, tryHideFullScreenLoading } from "@/components/Loading/fullScreen";
import { APP_CODE, LOGIN_URL } from "@/config";
import { ElMessage } from "element-plus";
import { ResultData } from "@/api/interface";
import { ResultEnum } from "@/enums/httpEnum";
import { checkStatus } from "./helper/checkStatus";
import { useUserStore } from "@/stores/modules/user";
import router from "@/routers";
import { isBlob, isEmpty } from "@/utils/is";
import { refreshToken } from "./modules/login";
import { useAuthStore } from "@/stores/modules/auth";
import useGlobalStore from "@/stores/modules/global";
import { getToken } from "./modules/auth";
import { getMessage } from "@/utils";
import { createFingerprint } from "@/hooks/useFingerprint";
import { useDebounceFn } from "@vueuse/core";
import { API_PREFIX } from "./config/servicePort";

export interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  loading?: boolean;
}

const debouncedGetToken = useDebounceFn(
  async () => {
    const { data, success } = await getToken();
    if (success) {
      useUserStore().setToken(data);
    }
  },
  300,
  { maxWait: 800 }
);

const config = {
  // 默认地址请求地址，可在 .env.** 文件中修改
  baseURL: "",
  // 设置超时时间
  timeout: ResultEnum.TIMEOUT as number,
  // 跨域时候允许携带凭证
  withCredentials: true
};
class RequestHttp {
  service: AxiosInstance;
  isRefreshing: boolean;
  refreshSubscribers: Array<() => Promise<void>>;
  maxRefreshTime: number;
  refreshTime: number;
  lastCheckToken: number;
  public constructor(config: AxiosRequestConfig) {
    // instantiation
    this.service = axios.create(config);
    this.isRefreshing = false;
    this.refreshSubscribers = [];
    this.refreshTime = 0;
    this.maxRefreshTime = 3;
    this.lastCheckToken = new Date().getTime();

    /**
     * @description 请求拦截器
     * 客户端发送请求 -> [请求拦截器] -> 服务器
     * token校验(JWT) : 接受服务器返回的 token,存储到 vuex/pinia/本地储存当中
     */
    this.service.interceptors.request.use(
      async (config: CustomAxiosRequestConfig) => {
        const userStore = useUserStore();
        const authStore = useAuthStore();
        const globalStore = useGlobalStore();
        // 当前请求不需要显示 loading，在 api 服务中通过指定的第三个参数: { loading: false } 来控制
        config.loading ?? (config.loading = true);
        config.loading && showFullScreenLoading();

        // console.log(userStore, "userStoreuserStoreuserStoreuserStoreuserStore");

        if (config.headers && typeof config.headers.set === "function") {
          config.headers.set("Authorization", "Bearer " + userStore.token);
          // if (config.url?.indexOf(API_PREFIX) !== -1) {
          config.headers.set("Authorization", userStore.token);
          // }
        }

        const menuId = config.headers.get("menuId");
        const workflowId = config.headers.get("workflowId");
        const func = config.headers.get("func");
        const fingerprint = config.headers.get("fingerprint");
        if (isEmpty(func)) config.headers.set("func", globalStore.btnAction);
        if (isEmpty(menuId)) config.headers.set("menuId", authStore.menuId);
        if (isEmpty(workflowId)) config.headers.set("workflowId", authStore.workflowId);
        if (isEmpty(fingerprint)) {
          if (isEmpty(globalStore.fingerprint)) {
            await createFingerprint();
          }
          config.headers.set("fingerprint", globalStore.fingerprint);
        }

        // if (!config.url?.includes("get_token") && userStore.token) {
        //   const now = new Date().getTime();
        //   const checkTokenDiff = now - this.lastCheckToken;
        //   if (checkTokenDiff >= 10 * 1000 || checkTokenDiff <= 200) {
        //     this.lastCheckToken = now;
        //     await debouncedGetToken();
        //   }
        // }
        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(error);
      }
    );

    /**
     * @description 响应拦截器
     *  服务器换返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
     */
    this.service.interceptors.response.use(
      (response: AxiosResponse) => {
        const { data, config } = response;
        const userStore = useUserStore();
        tryHideFullScreenLoading();
        // 登陆失效
        if (data.code == ResultEnum.OVERDUE) {
          userStore.setToken("");
          router.replace(LOGIN_URL);
          ElMessage.error(data.msg);
          return Promise.reject(data);
        }
        // 全局错误信息拦截（防止下载文件的时候返回数据流，没有 code 直接报错）
        if (data.code != 200 && !isBlob(data) && data.type !== "Buffer") {
          ElMessage.error(getMessage(data.msg));
          // console.log(config.url);
          if (config?.url?.includes("get_token")) {
            useUserStore().setToken("");
            router.replace({ path: LOGIN_URL, query: { logout: 1, appCode: APP_CODE } });
          }
          // debugger;
          return Promise.reject(data);
        }
        // 成功请求（在页面上除非特殊情况，否则不用处理失败逻辑）
        if (config.url !== "/auth/refresh") {
          this.refreshTime = 0;
        }
        const globalStore = useGlobalStore();
        globalStore.setGlobalState("btnAction", "");

        /**
         * 处理数据格式
         */

        if (Array.isArray(data.rows)) {
          data.data = {
            list: data.rows,
            total: data.total,
            pageNum: 1,
            pageSize: 10
          };
        }

        // console.log(data, "datadatadatadatadatadatadata");

        return data;
      },
      async (error: AxiosError) => {
        const { response, config } = error;
        console.log(config?.url);
        useGlobalStore().setGlobalState("btnAction", "");
        tryHideFullScreenLoading();
        if (config?.url?.includes("get_token") && useUserStore().token) {
          router.replace({ path: LOGIN_URL, query: { logout: 1, appCode: APP_CODE } });
        }
        // 请求超时 && 网络错误单独判断，没有 response
        if (error.message.indexOf("timeout") !== -1) ElMessage.error("请求超时！请您稍后重试");
        if (error.message.indexOf("Network Error") !== -1) ElMessage.error("网络错误！请您稍后重试");

        // 根据服务器响应的错误状态码，做不同的处理
        if (response) {
          if (response.status === ResultEnum.OVERDUE) {
            if (this.refreshTime >= this.maxRefreshTime) {
              useUserStore().setToken("");
              router.replace({ path: LOGIN_URL, query: { logout: 1, appCode: APP_CODE } });
              return Promise.reject(error);
            }
            if (!this.isRefreshing) {
              this.isRefreshing = true;
              const { data } = await refreshToken();

              this.refreshTime++;
              this.isRefreshing = false;
              useUserStore().setToken(data);
              const result = await this.service(config as any);
              this.refreshSubscribers.forEach(cb => cb());
              this.refreshSubscribers = [];
              return result;
            } else {
              return new Promise(resolve => {
                this.refreshSubscribers.push(async () => {
                  const data = await this.service(config as any);
                  resolve(data);
                });
              });
            }
          } else {
            checkStatus(response);
          }
        }

        // 服务器结果都没有返回(可能服务器错误可能客户端断网)，断网处理:可以跳转到断网页面
        if (!window.navigator.onLine) router.replace("/500");
        return Promise.reject(error);
      }
    );
  }
  /**
   * @description 常用请求方法封装
   */
  get<T>(url: string, params?: { [key: string]: any }, _object = {}): Promise<ResultData<T>> {
    if (!isEmpty(params)) {
      for (const key in params) {
        if (isEmpty(Reflect.get(params, key))) {
          delete params[key];
        }
      }
    }
    return this.service.get(url, { params, ..._object });
  }
  post<T>(url: string, params?: object | string, _object = {}): Promise<ResultData<T>> {
    return this.service.post(url, params, _object);
  }
  put<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.put(url, params, _object);
  }
  delete<T>(url: string, data?: any, _object = {}): Promise<ResultData<T>> {
    return this.service.delete(url, { data, ..._object });
  }
  download(url: string, params?: object, _object = {}): Promise<BlobPart> {
    return this.service.post(url, params, { ..._object, responseType: "blob" });
  }
}

export default new RequestHttp(config);
