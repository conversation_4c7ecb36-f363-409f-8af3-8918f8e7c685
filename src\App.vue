<template>
  <el-config-provider :locale="locale" :size="assemblySize" :button="buttonConfig">
    <router-view></router-view>
  </el-config-provider>
</template>

<script setup lang="ts">
import { onMounted, reactive, computed, watch } from "vue";
import { useI18n } from "vue-i18n";
import { getBrowserLang } from "@/utils";
import { useTheme } from "@/hooks/useTheme";
import { ElConfigProvider } from "element-plus";
import { useGlobalStore } from "@/stores/modules/global";
import en from "element-plus/es/locale/lang/en";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import useLanguageStore from "./stores/modules/language";
import { useFingerprint } from "./hooks/useFingerprint";
import { useIntervalFn, useUrlSearchParams } from "@vueuse/core";
// import { getToken } from "./api/modules/login";
import useUserStore from "./stores/modules/user";
import { useRoute, useRouter } from "vue-router";
import { LOGIN_URL } from "./config";
import { isProd } from "./utils/is";

const globalStore = useGlobalStore();

const userStore = useUserStore();

const router = useRouter();

const route = useRoute();

const languageStore = useLanguageStore();

const languageCode = computed(() => languageStore.languageCode ?? getBrowserLang());

// init theme
const { initTheme } = useTheme();
initTheme();
useFingerprint();

// const { pause } = useIntervalFn(
//   async () => {
//     if (route.fullPath.includes("login") || window.location.href.includes("login") || !userStore.token) return;
//
//     /* your function */
//     try {
//       const { data: token, success } = await getToken();
//       if (!success) {
//         pause();
//         const { appCode } = useUrlSearchParams<{ token: string; appCode: string }>("hash");
//         useUserStore().setToken("");
//         router.replace({ path: LOGIN_URL, query: { ...(appCode ? { appCode } : {}) } });
//       }
//     } catch (e) {
//       pause();
//       const { appCode } = useUrlSearchParams<{ token: string; appCode: string }>("hash");
//       useUserStore().setToken("");
//       router.replace({ path: LOGIN_URL, query: { ...(appCode ? { appCode } : {}) } });
//     }
//   },
//   5000,
//   {
//     immediate: true,
//     immediateCallback: true
//   }
// );

// function setZoomBasedOnScreenWidth() {
//   document.body.style.zoom = 0.8;
// }
// if (isProd()) {
//   window.onload = setZoomBasedOnScreenWidth;
// }

// init language
const i18n = useI18n();
onMounted(() => {
  const content = `
    版 本 号: ${__APP_INFO__.pkg.version}
    编译日期: ${__APP_INFO__.lastBuildTime}
  `;
  console.log(`%c${content} `, "color: #009dff; font-size: 16px");

  // globalStore.setGlobalState("language", language as LanguageType);
});
const setLanguage = () => {
  const language = languageStore.languageCode ?? getBrowserLang();
  i18n.locale.value = language;
  languageStore.setLanguageCode(language);
  const message = languageStore.languageData.reduce((prev, curr) => {
    return { ...prev, [curr.label]: curr.value };
  }, {});
  i18n.setLocaleMessage(language, message);
};

onMounted(async () => {
  await languageStore.getLanguageCodeList();
  setLanguage();
});

watch(languageCode, setLanguage, { deep: true, immediate: true });

// element language
const locale = computed(() => {
  console.log(languageStore.languageCode);
  if (languageStore.languageCode == "zh") return zhCn;
  if (languageStore.languageCode == "en") return en;
  return getBrowserLang() == "zh" ? zhCn : en;
});

// element assemblySize
const assemblySize = computed(() => globalStore.assemblySize);

// element button config
const buttonConfig = reactive({ autoInsertSpace: false });
</script>

<style>
.el-aside {
  --el-menu-bg-color: rgb(8 21 41) !important;
  --el-aside-logo-text-color: #ffffff;
  --el-menu-text-color: #ffffff !important;
  --el-menu-hover-text-color: #ffffff !important;
  --el-menu-hover-bg-color: rgb(255 255 255 / 30%);
}
</style>
