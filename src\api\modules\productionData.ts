import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { DimensionsItem } from "@/typings/productionData";
import { API_PREFIX } from "@/api/config/servicePort";

/**
 * Dimensions列表
 * @param params
 */
export const ipqaDimensionsList = (params: ReqPage) => {
  return http.post<DimensionsItem>(`${API_PREFIX}/productionData/ipqaMeasure/list`, params);
};

/**
 * Dimensions列表导入
 * @param params
 */
export const ipqaDimensionsImport = (params: ReqPage) => {
  return http.post(`${API_PREFIX}/productionData/ipqaMeasure/uploadExcelData`, params);
};
/**
 * Dimensions列表导入
 * @param params
 */
export const ipqaMeasureExportTmpl = (params: ReqPage) => {
  return http.get(`${API_PREFIX}/productionData/ipqaMeasure/exportTmpl`, params);
};
/**
 * Dimensions列表导入
 * @param params
 */
export const ipqaMeasureDel = (params: any) => {
  return http.post(`${API_PREFIX}/productionData/ipqaMeasure/del`, params);
};

/**
 * Dimensions编辑
 * @param params
 */
export const ipqaMeasureEdit = (params: any) => {
  return http.post(`${API_PREFIX}/productionData/ipqaMeasure/edit`, params);
};
/**
 * VisualData列表
 * @param params
 */
export const ipqaVmiList = (params: ReqPage) => {
  return http.post<DimensionsItem>(`${API_PREFIX}/productionData/ipqaVmi/list`, params);
};

/**
 * VisualData列表导入
 * @param params
 */
export const ipqaVmiImport = (params: ReqPage) => {
  return http.post(`${API_PREFIX}/productionData/ipqaVmi/uploadExcelData`, params);
};
/**
 * VisualData 编辑
 * @param params
 */
export const ipqaVmiEdit = (params: any) => {
  return http.post(`${API_PREFIX}/productionData/ipqaVmi/edit`, params);
};

/**
 * OQA-Dimensions列表
 * @param params
 */
export const oqaMeasureList = (params: ReqPage) => {
  return http.post<DimensionsItem>(`${API_PREFIX}/productionData/oqaMeasure/list`, params);
};
/**
 * OQA-Dimensions列表导入
 * @param params
 */
export const oqaMeasureImport = (params: ReqPage) => {
  return http.post(`${API_PREFIX}/productionData/oqaMeasure/uploadExcelData`, params);
};
/**
 * OQA-Dimensions编辑
 * @param params
 */
export const oqaMeasureEdit = (params: any) => {
  return http.post(`${API_PREFIX}/productionData/oqaMeasure/edit`, params);
};

/**
 * OQA-Dimensions删除
 * @param params
 */
export const oqaMeasureDel = (params: any) => {
  return http.post(`${API_PREFIX}/productionData/oqaMeasure/del`, params);
};
/**
 * OQA-VisualData列表
 * @param params
 */
export const visualdataList = (params: ReqPage) => {
  return http.post<DimensionsItem>(`${API_PREFIX}/productionData/oqaVmi/list`, params);
};

/**
 * OQA-Vmi列表导入
 * @param params
 */
export const oqaVmiImport = (params: ReqPage) => {
  return http.post(`${API_PREFIX}/productionData/oqaVmi/uploadExcelData`, params);
};

/**
 * OQA-VMI编辑
 * @param params
 */
export const oqaVmiEdit = (params: any) => {
  return http.post(`${API_PREFIX}/productionData/oqaVmi/edit`, params);
};
/**
 * OQA-VMI删除
 * @param params
 */
export const oqaVmiDel = (params: any) => {
  return http.post(`${API_PREFIX}/productionData/oqaVmi/del`, params);
};

/**
 * OQA-Dimensions列表导出
 * @param params
 */
export const ipqaMeasureExport = (params: ReqPage) => {
  return http.post(`${API_PREFIX}/productionData/ipqaMeasure/export`, params);
};

/**
 * OIPQA-VMI模板
 * @param params
 */
export const ipqaVmiExportTmpl = (params: ReqPage) => {
  return http.get(`${API_PREFIX}/productionData/ipqaVmi/exportTmpl`, params);
};
/**
 * IPQA-VMI导出
 * @param params
 */
export const ipqaVmiExport = (params: ReqPage) => {
  return http.post(`${API_PREFIX}/productionData/ipqaVmi/export`, params);
};
/**
 * IPQA-VMI删除
 * @param params
 */
export const ipqaVmiDel = (params: any) => {
  return http.post(`${API_PREFIX}/productionData/ipqaVmi/del`, params);
};
/**
 * OQA-Dimensionsm模板
 * @param params
 */
export const oqaMeasureExportTmpl = (params: ReqPage) => {
  return http.get(`${API_PREFIX}/productionData/oqaMeasure/exportTmpl`, params);
};
/**
 * OQA-Dimensions导出
 * @param params
 */
export const oqaMeasureExport = (params: ReqPage) => {
  return http.post(`${API_PREFIX}/productionData/oqaMeasure/export`, params);
};

/**
 * OQA-VMI模板
 * @param params
 */
export const oqaVmiExportTmpl = (params: ReqPage) => {
  return http.get(`${API_PREFIX}/productionData/oqaVmi/exportTmpl`, params);
};
/**
 * OQA-VMI导出
 * @param params
 */
export const oqaVmiExport = (params: ReqPage) => {
  return http.post(`${API_PREFIX}/productionData/oqaVmi/export`, params);
};
