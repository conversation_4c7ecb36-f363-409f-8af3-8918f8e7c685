<template>
  <div class="table-box" :key="pageKey">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @search="tableSearch"
      @reset="reset"
    >
      <template #appendTableTop>
        <div class="card table-main" style="margin-bottom: 10px">
          <div class="tab-wrap" style="text-align: center">
            <el-radio-group v-model="currentPage" size="large" @change="tabChange">
              <el-radio label="Lar" :value="1" />
              <el-radio label="TrendDefect Pareto" :value="2" />
            </el-radio-group>
          </div>
          <div :key="queryKey">
            <Lar v-if="currentPage === 1" :query-params="initParam" />
            <DefectPareto v-if="currentPage === 2" :query-params="initParam" />
          </div>
        </div>
      </template>
      <!--       表格 header 按钮 -->
      <!--       表格 header 按钮 -->
      <template #tableHeader>
        <!--              <el-button type="primary" @click="handleImport">Import</el-button>-->
        <el-button v-if="currentPage === 1" v-auth="'statistics:oqa:lar:export'" type="primary" @click="handleExport">
          Export
        </el-button>
        <el-button v-if="currentPage === 2" v-auth="'statistics:oqa:pareto:export'" type="primary" @click="handleExport">
          Export
        </el-button>
      </template>
    </ProTable>
    <ImportExcel ref="ImportExcelRef" />
  </div>
</template>

<script setup lang="tsx" name="dimensions">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import ImportExcel from "@/components/ImportExcel/index.vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { ipqaDimensionsImport, ipqaDimensionsList } from "@/api/modules/productionData";
import { DimensionsItem, DimensionsQuery } from "@/typings/productionData";
import Lar from "@/views/statistics/oqa/components/Lar.vue";
import DefectPareto from "@/views/statistics/ipqa/components/DefectPareto.vue";

import { isEmpty } from "@/utils/is";
import { useDict } from "@/hooks/useDict";
import VMIDataChart from "@/views/statistics/ipqa/components/VMIDataChart.vue";
import { downloadFile } from "@/utils";

import {
  ipqaVmiDataList,
  ipqaVmiDataYieldExport,
  oqaVmiDataLarExport,
  oqaVmiDataLarList,
  oqaVmiDataParetoExport,
  oqaVmiDataParetoList
} from "@/api/modules/productionDataAnalysis";
import { downloadFileByName } from "@/utils/download";
import { formatParams } from "@/utils/util";
import { getSupplierAll } from "@/api/modules/supplier";
import { Supplier } from "@/typings/supplier";
import DateRange from "@/views/components/DateRange.vue";
import { useI18n } from "vue-i18n";
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>();
const proTable = ref<ProTableInstance>();
const VMIDataChartRef = ref<InstanceType<typeof VMIDataChart>>();
const { check, currentRow } = useCheckSelectId();
const currentPage = ref(1);
const queryKey = ref(0);
const { t } = useI18n();
const initParam = reactive({
  pageSize: 10,
  pageNum: 1,
  condition: {
    supplier: "",
    product: "",
    model: "",
    workOrderNo: "",
    station: "",
    parameter: "",
    startDate: "",
    endDate: ""
  }
});
const { product_names, production_model, production_parameter } = useDict(
  "product_names",
  "production_model",
  "production_parameter"
);
const supplierList = ref<Supplier.Item[]>([]);
const pageKey = ref(0);
const columnsAll: any = {
  1: reactive<ColumnProps<DimensionsItem>[]>([
    { type: "index", label: "NO", width: 70 },
    {
      prop: "date",
      label: "Date",
      search: {
        el: "date-picker",
        render: () => {
          return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
        }
      }
    },
    {
      prop: "supplier",
      label: "Supplier",
      search: {
        el: "select",
        render: () => {
          return (
            <el-select v-model={initParam.supplier} placeholder={t("请选择")} clearable>
              {supplierList.value.map(item => (
                <el-option key={item.id} label={item.supplier} value={item.supplier} />
              ))}
            </el-select>
          );
        }
      }
    },
    {
      prop: "product",
      label: "Product",
      enum: product_names,
      search: {
        el: "select",
        render: () => {
          return (
            <el-select v-model={initParam.product} placeholder="请选择" clearable>
              {product_names.value.map(item => (
                <el-option key={item.value} label={item.label} value={item.value} />
              ))}
            </el-select>
          );
        }
      }
    },
    {
      prop: "model",
      label: "Model",
      enum: production_model,
      search: {
        el: "select",
        render: () => {
          return (
            <el-select v-model={initParam.model} placeholder="请选择" clearable>
              {production_model.value.map(item => (
                <el-option key={item.value} label={item.label} value={item.value} />
              ))}
            </el-select>
          );
        }
      }
    },
    {
      prop: "doNo",
      label: "Do",
      search: { el: "input", render: () => <el-input v-model={initParam.doNo} clearable placeholder="请输入" /> }
    },
    { prop: "totalLots", label: " Total Lots" },
    { prop: "acceptLots", label: "Accept Lots" },
    { prop: "lar", label: "LAR" }
  ]),
  2: reactive<ColumnProps<DimensionsItem>[]>([
    { type: "index", label: "NO", width: 70 },
    {
      prop: "date",
      label: "Date",
      search: {
        el: "date-picker",
        render: () => {
          return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
        }
      }
    },
    {
      prop: "supplier",
      label: "Supplier",
      search: {
        el: "select",
        render: () => {
          return (
            <el-select v-model={initParam.supplier} placeholder="请选择" clearable>
              {supplierList.value.map(item => (
                <el-option key={item.id} label={item.supplier} value={item.supplier} />
              ))}
            </el-select>
          );
        }
      }
    },
    {
      prop: "product",
      label: "Product",
      enum: product_names,
      search: {
        el: "select",
        render: () => {
          return (
            <el-select v-model={initParam.product} placeholder="请选择" clearable>
              {product_names.value.map(item => (
                <el-option key={item.value} label={item.label} value={item.value} />
              ))}
            </el-select>
          );
        }
      }
    },
    {
      prop: "model",
      label: "Model",
      enum: production_model,
      search: {
        el: "select",
        render: () => {
          return (
            <el-select v-model={initParam.model} placeholder="请选择" clearable>
              {production_model.value.map(item => (
                <el-option key={item.value} label={item.label} value={item.value} />
              ))}
            </el-select>
          );
        }
      }
    },
    {
      prop: "doNo",
      label: "Do",
      search: { el: "input", render: () => <el-input v-model={initParam.doNo} clearable placeholder="请输入" /> }
    },
    { prop: "lotNo", label: "Lot No" },
    { prop: "defectCategory", label: "Defect Category" },
    {
      prop: "defectQty",
      label: "Defect Qty"
    }
  ])
};

const columns = ref(columnsAll[currentPage.value]);
let queryParams = reactive<DimensionsQuery.IQueryParams>({} as DimensionsQuery.IQueryParams);

const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  // const { pageNum, pageSize, ...condition } = params;
  //
  // if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
  //   condition.startDate = filterDate.value[0];
  //   condition.endDate = filterDate.value[1];
  // }
  // queryParams = reactive(condition);
  const result = formatParams(initParam, params, filterDate.value);
  initParam.pageNum = result.pageNum;
  initParam.pageSize = result.pageSize;
  // console.log(initParam);
  return currentPage.value === 1 ? oqaVmiDataLarList(result) : oqaVmiDataParetoList(result);
};

const tableSearch = () => {
  queryKey.value++;
};

const tabChange = (val: number) => {
  // currentPage.value = val;
  columns.value = columnsAll[val];
  pageKey.value++;
  // console.log(proTable.value, "11111111111111111");
  // debugger;
  // nextTick(proTable.value?.search);
};

/**
 * 批量导入
 */
const handleImport = () => {
  const params = {
    title: "IPQA Dimensions",
    importApi: ipqaDimensionsImport,
    getTableList: proTable.value?.getTableList
  };
  ImportExcelRef.value?.acceptParams(params);
};
/**
 * 导出
 */

const handleExport = async () => {
  if (currentPage.value === 1) {
    await downloadFileByName(oqaVmiDataLarExport, initParam.condition);
  }
  if (currentPage.value === 2) {
    await downloadFileByName(oqaVmiDataParetoExport, initParam.condition);
  }
};

const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};

const reset = async () => {
  resetCounter.value++;
  filterDate.value = [];
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  await nextTick(proTable.value?.getTableList);
  queryKey.value++;
};

onMounted(() => {
  getSupplierData();
});
</script>
