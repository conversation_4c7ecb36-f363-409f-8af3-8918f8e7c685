<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
    >
      <!--       表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'production:ipqa:vmi:import'" type="primary" @click="handleImport">Import</el-button>
        <el-button v-auth="'production:ipqa:vmi:export'" type="primary" @click="handleExport">Export</el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="{ row }">
        <el-button v-auth="'production:ipqa:vmi:edit'" type="primary" link @click="handleModalOperation('Edit', row)">
          Edit
        </el-button>
        <el-button v-auth="'production:ipqa:vmi:del'" type="danger" link @click="handleRemove(row)">Del</el-button>
      </template>
    </ProTable>
    <ImportExcel ref="ImportExcelRef" />
    <handleModal ref="handleModalRef" />
  </div>
</template>

<script setup lang="tsx" name="dimensions">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { ref, reactive, onMounted, nextTick, computed } from "vue";
import ImportExcel from "@/components/ImportExcel/index.vue";
import {
  ipqaDimensionsList,
  ipqaMeasureDel,
  ipqaMeasureEdit,
  ipqaMeasureExport,
  ipqaVmiDel,
  ipqaVmiEdit,
  ipqaVmiExport,
  ipqaVmiExportTmpl,
  ipqaVmiImport,
  ipqaVmiList
} from "@/api/modules/productionData";
import DateRange from "@/views/components/DateRange.vue";
import { useDict } from "@/hooks/useDict";
import { DimensionsItem, DimensionsQuery } from "@/typings/productionData";
import { Supplier } from "@/typings/supplier";
import { getSupplierAll } from "@/api/modules/supplier";
import { ReqPage } from "@/api/interface";
import { isEmpty } from "@/utils/is";
import { formatParams } from "@/utils/util";
import { downloadFileByName } from "@/utils/download";
import { ExceptionManageItem } from "@/typings/exception-manage";
import { useHandleData } from "@/hooks/useHandleData";
import handleModal from "@/views/ipqa/visual-data/components/handleModal.vue";
import { getOperationColWidth, visibleOperationCol } from "@/utils";
import { useAuthStore } from "@/stores/modules/auth";
import useUserStore from "@/stores/modules/user";
import { useI18n } from "vue-i18n";
const handleModalRef = ref<InstanceType<typeof handleModal>>();
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>();
const proTable = ref<ProTableInstance>();
const auth = useAuthStore();
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const isSupplier = userInfo.value.roles.find((item: any) => {
  return item?.roleKey.indexOf("supplier") > -1;
});
const { t } = useI18n();
// const { check, currentRow } = useCheckSelectId();
const supplierList = ref<Supplier.Item[]>([]);

let initParam = reactive<Partial<DimensionsItem & ReqPage>>({
  pageSize: 10,
  pageNum: 1,
  supplier: userInfo.value.supplier,
  product: undefined,
  model: undefined,
  workOrderNo: undefined,
  station: undefined,
  startDate: undefined,
  endDate: undefined
});
const { product_names, production_model } = useDict("product_names", "production_model");
const pageButtons = ["production:ipqa:vmi:edit", "production:ipqa:vmi:del"];
const columns = reactive<ColumnProps<DimensionsItem>[]>([
  // { type: "selection", fixed: "left", width: 70 },
  { type: "index", label: "NO", width: 70 },
  {
    prop: "date",
    label: "Date",
    search: {
      el: "date-picker",
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  {
    prop: "supplier",
    label: "Supplier",
    search: {
      el: "select",
      render: () => {
        return !isSupplier ? (
          <el-select v-model={initParam.supplier} placeholder={t("请选择")} clearable>
            {supplierList.value.map(item => (
              <el-option key={item.id} label={item.supplier} value={item.supplier} />
            ))}
          </el-select>
        ) : (
          <el-input disabled v-model={userInfo.value.supplier} clearable placeholder="请输入" />
        );
      }
    }
  },
  {
    prop: "product",
    label: "Product",
    enum: product_names,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.product} placeholder={t("请选择")} clearable>
            {product_names.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "model",
    label: "Model",
    enum: production_model,
    search: {
      el: "select",
      render: () => {
        return (
          <el-select v-model={initParam.model} placeholder={t("请选择")} clearable>
            {production_model.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "workOrderNo",
    label: "Work Order No",
    search: { el: "input", render: () => <el-input v-model={initParam.workOrderNo} clearable placeholder={t("请输入")} /> }
  },
  {
    prop: "station",
    label: "Station",
    search: { el: "input", render: () => <el-input v-model={initParam.station} clearable placeholder={t("请输入")} /> }
  },
  { prop: "defectCategory", label: "Defect Category" },
  { prop: "defectQty", label: "Defect Qty" },
  { prop: "outputQty", label: "Output Qty" },
  { prop: "createBy", label: "Creator" },
  { prop: "createTime", label: "Create Time" },
  { prop: "remark", label: "Remark" },
  ...(visibleOperationCol(auth.authButtonList, pageButtons)
    ? [
        {
          prop: "operation",
          label: "Operation",
          width: getOperationColWidth(auth.authButtonList, pageButtons),
          fixed: "right"
        }
      ]
    : [])
]);
let queryParams = reactive<DimensionsQuery.IQueryParams>({} as DimensionsQuery.IQueryParams);

const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  const result = formatParams(initParam, params, filterDate.value);
  // initParam.pageNum = result.pageNum;
  // initParam.pageSize = result.pageSize;
  initParam = result;

  if (isSupplier) {
    result.condition.supplier = userInfo.value.supplier;
  }
  return ipqaVmiList(result);
};

/**
 * 批量导入
 */
const handleImport = () => {
  const params = {
    title: "Visual Data",
    importApi: ipqaVmiImport,
    getTableList: proTable.value?.getTableList,
    tempFun: ipqaVmiExportTmpl
  };
  ImportExcelRef.value?.acceptParams(params);
};

const handleExport = async () => {
  await downloadFileByName(ipqaVmiExport, initParam.condition);
};

const getSupplierData = async () => {
  const { data } = await getSupplierAll("");
  supplierList.value = data;
};

const reset = async () => {
  resetCounter.value++;
  filterDate.value = [];
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }

  nextTick(proTable.value?.getTableList);
};

const handleModalOperation = (title: string, row?: ExceptionManageItem) => {
  const form = isEmpty(row?.id) ? {} : { ...row };
  const params = {
    title,
    isView: title === "查看",
    form,
    api: ipqaVmiEdit,
    getTableList: proTable.value?.getTableList
  };
  handleModalRef.value?.acceptParams(params);
};

/**
 * 删除
 * @param row
 */
const handleRemove = async (row: DimensionsItem) => {
  await useHandleData(ipqaVmiDel, { ids: [row.id] }, `删除所选数据`);
  proTable.value?.getTableList();
};
onMounted(async () => {
  await getSupplierData();
});
</script>
